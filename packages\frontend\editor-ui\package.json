{"name": "n8n-editor-ui", "version": "1.97.0", "description": "Workflow Editor UI for n8n", "main": "index.js", "scripts": {"clean": "rimraf dist .turbo", "build": "cross-env VUE_APP_PUBLIC_PATH=\"/{{BASE_PATH}}/\" NODE_OPTIONS=\"--max-old-space-size=8192\" vite build", "typecheck": "vue-tsc --noEmit", "typecheck:watch": "vue-tsc --watch --noEmit", "dev": "pnpm serve", "lint": "eslint src --ext .js,.ts,.vue --quiet", "lintfix": "eslint src --ext .js,.ts,.vue --fix", "format": "biome format --write . && prettier --write . --ignore-path ../../../.prettierignore", "format:check": "biome ci . && prettier --check . --ignore-path ../../../.prettierignore", "serve": "cross-env VUE_APP_URL_BASE_API=http://localhost:5678/ vite --host 0.0.0.0 --port 8080 dev", "test": "vitest run", "test:dev": "vitest --silent=false"}, "dependencies": {"@codemirror/autocomplete": "^6.16.0", "@codemirror/commands": "^6.5.0", "@codemirror/lang-css": "^6.0.1", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/language": "^6.10.1", "@codemirror/lint": "^6.8.0", "@codemirror/search": "^6.5.6", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.26.3", "@dagrejs/dagre": "^1.1.4", "@lezer/common": "1.1.0", "@n8n/rest-api-client": "workspace:*", "@n8n/api-types": "workspace:*", "@n8n/chat": "workspace:*", "@n8n/codemirror-lang": "workspace:*", "@n8n/codemirror-lang-sql": "^1.0.2", "@n8n/composables": "workspace:*", "@n8n/constants": "workspace:*", "@n8n/design-system": "workspace:*", "@n8n/i18n": "workspace:*", "@n8n/permissions": "workspace:*", "@n8n/stores": "workspace:*", "@n8n/utils": "workspace:*", "@replit/codemirror-indentation-markers": "^6.5.3", "@sentry/vue": "catalog:frontend", "@typescript/vfs": "^1.6.0", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.42.1", "@vue-flow/minimap": "^1.5.2", "@vue-flow/node-resizer": "^1.4.0", "@vueuse/components": "^10.11.0", "@vueuse/core": "catalog:frontend", "array.prototype.tosorted": "1.1.4", "axios": "catalog:", "bowser": "2.11.0", "change-case": "^5.4.4", "chart.js": "^4.4.0", "codemirror-lang-html-n8n": "^1.0.0", "comlink": "^4.4.1", "core-js": "^3.40.0", "curlconverter": "^4.12.0", "dateformat": "^3.0.3", "element-plus": "catalog:frontend", "email-providers": "^2.0.1", "esprima-next": "5.8.4", "fast-json-stable-stringify": "^2.1.0", "file-saver": "^2.0.2", "flatted": "catalog:", "highlight.js": "catalog:frontend", "humanize-duration": "^3.27.2", "jsonpath": "^1.1.1", "lodash": "catalog:", "luxon": "catalog:", "n8n-workflow": "workspace:*", "pinia": "catalog:frontend", "prettier": "^3.3.3", "qrcode.vue": "^3.3.4", "stream-browserify": "^3.0.0", "timeago.js": "^4.0.2", "typescript": "catalog:frontend", "uuid": "catalog:", "v3-infinite-loading": "^1.2.2", "vue": "catalog:frontend", "vue-agile": "^2.0.0", "vue-chartjs": "^5.2.0", "vue-github-button": "^3.1.3", "vue-i18n": "catalog:frontend", "vue-json-pretty": "2.2.4", "vue-markdown-render": "catalog:frontend", "vue-router": "catalog:frontend", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-touch-events": "^4.1.3", "vuedraggable": "4.1.0", "web-tree-sitter": "0.24.3", "xss": "catalog:"}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@iconify/json": "^2.2.228", "@n8n/eslint-config": "workspace:*", "@n8n/typescript-config": "workspace:*", "@n8n/vitest-config": "workspace:*", "@pinia/testing": "^0.1.6", "@types/dateformat": "^3.0.0", "@types/file-saver": "^2.0.1", "@types/humanize-duration": "^3.27.1", "@types/json-schema": "^7.0.15", "@types/jsonpath": "^0.2.0", "@types/lodash": "catalog:", "@types/uuid": "catalog:", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "catalog:frontend", "@vitest/coverage-v8": "catalog:frontend", "browserslist-to-esbuild": "^2.1.1", "fake-indexeddb": "^6.0.0", "miragejs": "^0.1.48", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.2", "vite": "catalog:frontend", "vite-plugin-static-copy": "2.2.0", "vite-svg-loader": "5.1.0", "vitest": "catalog:frontend", "vitest-mock-extended": "catalog:frontend", "vue-tsc": "catalog:frontend"}, "peerDependencies": {"@fortawesome/fontawesome-svg-core": "*", "@fortawesome/free-regular-svg-icons": "*", "@fortawesome/free-solid-svg-icons": "*", "@fortawesome/vue-fontawesome": "*"}}