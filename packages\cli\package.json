{"name": "n8n", "version": "1.97.0", "description": "n8n Workflow Automation Tool", "main": "dist/index", "types": "dist/index.d.ts", "oclif": {"commands": "./dist/commands", "helpClass": "./dist/help", "bin": "n8n"}, "scripts": {"clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json && node scripts/build.mjs", "buildAndDev": "pnpm run build && pnpm run dev", "dev": "concurrently -k -n \"TypeScript,Node\" -c \"yellow.bold,cyan.bold\" \"npm run watch\" \"nodemon\"", "dev:worker": "concurrently -k -n \"TypeScript,Node\" -c \"yellow.bold,cyan.bold\" \"npm run watch\" \"nodemon worker\"", "dev:webhook": "concurrently -k -n \"TypeScript,Node\" -c \"yellow.bold,cyan.bold\" \"npm run watch\" \"nodemon webhook\"", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "start": "run-script-os", "start:default": "cd bin && ./n8n", "start:windows": "cd bin && n8n", "test": "pnpm test:sqlite", "test:dev": "N8N_LOG_LEVEL=silent DB_TYPE=sqlite jest --watch", "test:sqlite": "N8N_LOG_LEVEL=silent DB_TYPE=sqlite jest", "test:postgres": "N8N_LOG_LEVEL=silent DB_TYPE=postgresdb DB_POSTGRESDB_SCHEMA=alt_schema DB_TABLE_PREFIX=test_ jest --no-coverage", "test:mariadb": "N8N_LOG_LEVEL=silent DB_TYPE=mariadb DB_TABLE_PREFIX=test_ jest --no-coverage", "test:mysql": "N8N_LOG_LEVEL=silent DB_TYPE=mysqldb DB_TABLE_PREFIX=test_ jest --no-coverage", "watch": "tsc-watch -p tsconfig.build.json --onCompilationComplete \"tsc-alias -p tsconfig.build.json\""}, "bin": {"n8n": "./bin/n8n"}, "keywords": ["automate", "automation", "IaaS", "iPaaS", "n8n", "workflow"], "engines": {"node": ">=20.19 <= 24.x"}, "files": ["bin", "templates", "dist", "!dist/**/e2e.*"], "devDependencies": {"@n8n/typescript-config": "workspace:*", "@redocly/cli": "^1.28.5", "@types/aws4": "^1.5.1", "@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.5", "@types/convict": "^6.1.1", "@types/cookie-parser": "^1.4.8", "@types/express": "catalog:", "@types/flat": "^5.0.5", "@types/formidable": "^3.4.5", "@types/json-diff": "^1.0.0", "@types/jsonwebtoken": "catalog:", "@types/lodash": "catalog:", "@types/psl": "^1.1.0", "@types/replacestream": "^4.0.1", "@types/shelljs": "^0.8.11", "@types/sshpk": "^1.17.4", "@types/superagent": "^8.1.9", "@types/swagger-ui-express": "^4.1.8", "@types/syslog-client": "^1.1.2", "@types/uuid": "catalog:", "@types/validator": "^13.7.0", "@types/ws": "^8.18.1", "@types/xml2js": "catalog:", "@types/yamljs": "^0.2.31", "@vvo/tzdb": "^6.141.0", "concurrently": "^8.2.0", "ioredis-mock": "^8.8.1", "mjml": "^4.15.3", "openapi-types": "^12.1.3", "ts-essentials": "^7.0.3"}, "dependencies": {"@aws-sdk/client-secrets-manager": "3.808.0", "@azure/identity": "4.3.0", "@azure/keyvault-secrets": "4.8.0", "@google-cloud/secret-manager": "5.6.0", "@n8n/ai-workflow-builder": "workspace:*", "@n8n/api-types": "workspace:*", "@n8n/backend-common": "workspace:^", "@n8n/client-oauth2": "workspace:*", "@n8n/config": "workspace:*", "@n8n/constants": "workspace:^", "@n8n/db": "workspace:^", "@n8n/decorators": "workspace:*", "@n8n/di": "workspace:*", "@n8n/integration-test-utils": "workspace:^", "@n8n/localtunnel": "3.0.0", "@n8n/n8n-nodes-langchain": "workspace:*", "@n8n/permissions": "workspace:*", "@n8n/task-runner": "workspace:*", "@n8n/typeorm": "catalog:", "@n8n_io/ai-assistant-sdk": "catalog:", "@n8n_io/license-sdk": "2.21.0", "@oclif/core": "4.0.7", "@rudderstack/rudder-sdk-node": "2.1.4", "@sentry/node": "catalog:", "aws4": "1.11.0", "axios": "catalog:", "bcryptjs": "2.4.3", "bull": "4.16.4", "cache-manager": "5.2.3", "change-case": "4.1.2", "class-transformer": "0.5.1", "class-validator": "0.14.0", "compression": "1.8.0", "convict": "6.2.4", "cookie-parser": "1.4.7", "csrf": "3.1.0", "dotenv": "8.6.0", "express": "5.1.0", "express-handlebars": "8.0.1", "express-openapi-validator": "5.5.3", "express-prom-bundle": "8.0.0", "express-rate-limit": "7.5.0", "fast-glob": "catalog:", "flat": "5.0.2", "flatted": "catalog:", "formidable": "3.5.4", "handlebars": "4.7.8", "helmet": "8.1.0", "infisical-node": "1.3.0", "ioredis": "5.3.2", "isbot": "3.6.13", "json-diff": "1.0.6", "jsonschema": "1.4.1", "jsonwebtoken": "catalog:", "ldapts": "4.2.6", "lodash": "catalog:", "luxon": "catalog:", "mysql2": "3.11.0", "n8n-core": "workspace:*", "n8n-editor-ui": "workspace:*", "n8n-nodes-base": "workspace:*", "n8n-workflow": "workspace:*", "nanoid": "catalog:", "nodemailer": "6.9.9", "oauth-1.0a": "2.2.6", "open": "7.4.2", "otpauth": "9.1.1", "p-cancelable": "2.1.1", "p-lazy": "3.1.0", "pg": "8.12.0", "picocolors": "catalog:", "pkce-challenge": "5.0.0", "posthog-node": "3.2.1", "prom-client": "15.1.3", "psl": "1.9.0", "raw-body": "3.0.0", "reflect-metadata": "catalog:", "replacestream": "4.0.3", "samlify": "2.10.0", "semver": "7.5.4", "shelljs": "0.8.5", "simple-git": "3.17.0", "source-map-support": "0.5.21", "sqlite3": "5.1.7", "sshpk": "1.18.0", "swagger-ui-express": "5.0.1", "syslog-client": "1.1.1", "uuid": "catalog:", "validator": "13.7.0", "ws": "8.17.1", "xml2js": "catalog:", "xmllint-wasm": "3.0.1", "xss": "catalog:", "yamljs": "0.3.0", "zod": "catalog:"}}