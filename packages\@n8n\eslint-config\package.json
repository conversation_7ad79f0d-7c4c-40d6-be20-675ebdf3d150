{"name": "@n8n/eslint-config", "private": true, "version": "0.0.1", "exports": {"./base": "./base.js", "./frontend": "./frontend.js", "./local-rules": "./local-rules.js", "./node": "./node.js", "./shared": "./shared.js"}, "devDependencies": {"@types/eslint": "^8.56.5", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vue/eslint-config-typescript": "^13.0.0", "eslint": "^8.57.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-n8n-local-rules": "^1.0.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-plugin-unused-imports": "^3.1.0", "eslint-plugin-vue": "^9.23.0", "vue-eslint-parser": "^9.4.2"}, "scripts": {"clean": "rimraf .turbo", "test": "jest"}}