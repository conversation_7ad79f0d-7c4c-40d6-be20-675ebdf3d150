{"name": "n8n-core", "version": "1.96.0", "description": "Core functionality of n8n", "main": "dist/index", "types": "dist/index.d.ts", "bin": {"n8n-copy-static-files": "./bin/copy-static-files", "n8n-generate-translations": "./bin/generate-translations", "n8n-generate-metadata": "./bin/generate-metadata"}, "scripts": {"clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "pnpm watch", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "watch": "tsc-watch -p tsconfig.build.json --onCompilationComplete \"tsc-alias -p tsconfig.build.json\"", "test": "jest", "test:dev": "jest --watch"}, "files": ["dist", "bin"], "devDependencies": {"@n8n/typescript-config": "workspace:*", "@types/express": "catalog:", "@types/jsonwebtoken": "catalog:", "@types/lodash": "catalog:", "@types/mime-types": "^2.1.0", "@types/uuid": "catalog:", "@types/xml2js": "catalog:"}, "dependencies": {"@aws-sdk/client-s3": "3.808.0", "@langchain/core": "catalog:", "@n8n/backend-common": "workspace:^", "@n8n/client-oauth2": "workspace:*", "@n8n/config": "workspace:*", "@n8n/constants": "workspace:*", "@n8n/decorators": "workspace:*", "@n8n/di": "workspace:*", "@sentry/node": "catalog:", "axios": "catalog:", "callsites": "catalog:", "chardet": "2.0.0", "cron": "3.1.7", "fast-glob": "catalog:", "file-type": "16.5.4", "form-data": "catalog:", "iconv-lite": "catalog:", "jsonwebtoken": "catalog:", "lodash": "catalog:", "luxon": "catalog:", "mime-types": "2.1.35", "n8n-workflow": "workspace:*", "nanoid": "catalog:", "oauth-1.0a": "2.2.6", "p-cancelable": "2.1.1", "picocolors": "catalog:", "pretty-bytes": "5.6.0", "qs": "6.11.0", "ssh2": "1.15.0", "uuid": "catalog:", "winston": "3.14.2", "xml2js": "catalog:", "zod": "catalog:"}}