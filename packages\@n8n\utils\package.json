{"name": "@n8n/utils", "type": "module", "version": "1.10.0", "files": ["dist"], "exports": {"./dist/*": {"types": "./dist/*.d.ts", "import": "./dist/*.js", "require": "./dist/*.cjs"}, "./*": {"types": "./dist/*.d.ts", "import": "./dist/*.js", "require": "./dist/*.cjs"}}, "scripts": {"dev": "vite", "build": "pnpm run typecheck && tsup", "preview": "vite preview", "typecheck": "tsc --noEmit", "test": "vitest run", "test:dev": "vitest --silent=false", "lint": "eslint src --ext .js,.ts,.vue --quiet", "lintfix": "eslint src --ext .js,.ts,.vue --fix", "format": "biome format --write . && prettier --write . --ignore-path ../../../.prettierignore", "format:check": "biome ci . && prettier --check . --ignore-path ../../../.prettierignore"}, "devDependencies": {"@n8n/eslint-config": "workspace:*", "@n8n/typescript-config": "workspace:*", "@n8n/vitest-config": "workspace:*", "@testing-library/jest-dom": "catalog:frontend", "@testing-library/user-event": "catalog:frontend", "tsup": "catalog:", "typescript": "catalog:frontend", "vite": "catalog:frontend", "vitest": "catalog:frontend"}, "license": "See LICENSE.md file in the root of the repository"}