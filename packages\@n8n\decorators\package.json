{"name": "@n8n/decorators", "version": "0.7.0", "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint .", "lintfix": "eslint . --fix", "watch": "tsc -p tsconfig.build.json --watch", "test": "jest", "test:dev": "jest --watch"}, "main": "dist/index.js", "module": "src/index.ts", "types": "dist/index.d.ts", "files": ["dist/**/*"], "devDependencies": {"@n8n/typescript-config": "workspace:*", "@types/express": "catalog:", "@types/lodash": "catalog:"}, "dependencies": {"@n8n/constants": "workspace:^", "@n8n/di": "workspace:^", "@n8n/permissions": "workspace:^", "lodash": "catalog:", "n8n-workflow": "workspace:^"}}