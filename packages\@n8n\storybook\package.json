{"name": "@n8n/storybook", "private": true, "version": "0.0.1", "devDependencies": {"@chromatic-com/storybook": "^3.2.5", "@storybook/addon-a11y": "^8.6.4", "@storybook/addon-actions": "^8.6.4", "@storybook/addon-docs": "^8.6.4", "@storybook/addon-essentials": "^8.6.4", "@storybook/addon-interactions": "^8.6.4", "@storybook/addon-links": "^8.6.4", "@storybook/addon-themes": "^8.6.4", "@storybook/blocks": "^8.6.4", "@storybook/test": "^8.6.4", "@storybook/vue3": "^8.6.4", "@storybook/vue3-vite": "^8.6.4", "chromatic": "^11.27.0", "storybook": "^8.6.4"}}