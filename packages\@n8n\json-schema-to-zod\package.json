{"name": "@n8n/json-schema-to-zod", "version": "1.3.0", "description": "Converts JSON schema objects into Zod schemas", "types": "./dist/types/index.d.ts", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "exports": {"import": {"types": "./dist/types/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/types/index.d.ts", "default": "./dist/cjs/index.js"}}, "scripts": {"clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit", "dev": "tsc -w", "format": "biome format --write src", "format:check": "biome ci src", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "build:types": "tsc -p tsconfig.types.json", "build:cjs": "tsc -p tsconfig.cjs.json && node postcjs.js", "build:esm": "tsc -p tsconfig.esm.json && node postesm.js", "build": "rimraf ./dist && pnpm run build:types && pnpm run build:cjs && pnpm run build:esm", "dry": "pnpm run build && pnpm pub --dry-run", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["zod", "json", "schema", "converter", "cli"], "author": "<PERSON>", "contributors": ["<PERSON> (https://github.com/werifu)", "<PERSON><PERSON> (https://github.com/ncardoso-barracuda)", "<PERSON> (https://github.com/lstrojny)", "<PERSON>v<PERSON><PERSON> (https://github.com/navtoj)", "<PERSON> (https://github.com/benmccann)", "<PERSON> (https://github.com/DZakh)", "<PERSON> (https://github.com/grimly)", "<PERSON> (https://github.com/david<PERSON>t)", "pevisscher (https://github.com/pevisscher)", "<PERSON><PERSON> (https://github.com/aidinabedi)", "<PERSON> (https://github.com/brettz9)", "n8n (https://github.com/n8n-io)"], "license": "ISC", "repository": {"type": "git", "url": "https://github.com/n8n-io/n8n"}, "peerDependencies": {"zod": "^3.0.0"}, "devDependencies": {"@n8n/typescript-config": "workspace:*", "@types/json-schema": "^7.0.15", "zod": "catalog:"}}