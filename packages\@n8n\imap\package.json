{"name": "@n8n/imap", "version": "0.10.0", "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json", "format": "biome format --write src test", "format:check": "biome ci src test", "lint": "eslint . --quiet", "lintfix": "eslint . --fix", "watch": "tsc -p tsconfig.build.json --watch", "test": "jest"}, "main": "dist/index.js", "module": "src/index.ts", "types": "dist/index.d.ts", "files": ["dist/**/*"], "dependencies": {"iconv-lite": "catalog:", "imap": "0.8.19", "quoted-printable": "1.0.1", "utf8": "3.0.0", "uuencode": "0.0.4"}, "devDependencies": {"@n8n/typescript-config": "workspace:*", "@types/imap": "^0.8.40", "@types/quoted-printable": "^1.0.2", "@types/utf8": "^3.0.3", "@types/uuencode": "^0.0.3"}}