{"name": "@n8n/config", "version": "1.41.0", "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm watch", "typecheck": "tsc --noEmit", "build": "tsc -p tsconfig.build.json", "format": "biome format --write src test", "format:check": "biome ci src test", "lint": "eslint .", "lintfix": "eslint . --fix", "watch": "tsc -p tsconfig.build.json --watch", "test": "jest", "test:dev": "jest --watch"}, "main": "dist/index.js", "module": "src/index.ts", "types": "dist/index.d.ts", "files": ["dist/**/*"], "dependencies": {"@n8n/di": "workspace:*", "reflect-metadata": "catalog:", "zod": "catalog:"}, "devDependencies": {"@n8n/typescript-config": "workspace:*"}}